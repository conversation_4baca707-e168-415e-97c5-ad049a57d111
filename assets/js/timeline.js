import { mountFloatingHearts, revealOnView, smoothScrollTo } from './utils.js';

async function loadTimelineData() {
  const res = await fetch('data/timeline.json');
  if (!res.ok) throw new Error('Failed to load timeline.json');
  return res.json();
}

function createTick(dateText) {
  const div = document.createElement('div');
  div.className = 'tick fade-in';
  div.textContent = dateText;
  return div;
}

function createEntry(item) {
  const wrapper = document.createElement('article');
  wrapper.className = 'entry';
  const card = document.createElement('div');
  card.className = 'card';
  const img = document.createElement('img');
  img.src = item.image;
  img.alt = item.title || 'Memory photo';
  img.onerror = () => { img.src = 'https://images.unsplash.com/photo-1517524206127-48bbd363f3ae?q=80&w=1600&auto=format&fit=crop'; };
  const content = document.createElement('div');
  content.className = 'content';
  const date = document.createElement('div');
  date.className = 'date';
  date.textContent = item.date;
  const title = document.createElement('h3');
  title.className = 'title';
  title.textContent = item.title || '';
  const p = document.createElement('p');
  p.className = 'desc';
  p.textContent = item.text || '';

  content.append(date, title, p);
  card.append(img, content);
  wrapper.append(card);
  return wrapper;
}

function observeBottomTrigger(callback) {
  const sentinel = document.createElement('div');
  sentinel.style.height = '1px';
  document.body.appendChild(sentinel);
  const io = new IntersectionObserver(entries => {
    entries.forEach(e => {
      if (e.isIntersecting) {
        callback();
        io.disconnect();
      }
    });
  }, { root: null, threshold: 0.1 });
  io.observe(sentinel);
}

window.addEventListener('DOMContentLoaded', async () => {
  mountFloatingHearts(document.body, 14);

  const axis = document.querySelector('.axis');
  const entriesEl = document.querySelector('.entries');
  const scrollHint = document.querySelector('.scroll-hint');

  try {
    const data = await loadTimelineData();

    data.items.forEach((item, idx) => {
      axis.appendChild(createTick(item.date));
      const entryEl = createEntry(item);
      entriesEl.appendChild(entryEl);
    });

    revealOnView('.tick');
    revealOnView('.card');

    // Smooth scroll between entries when clicking on axis ticks
    axis.addEventListener('click', (e) => {
      const target = e.target;
      if (target.classList.contains('tick')) {
        const idx = Array.from(axis.children).indexOf(target);
        const entry = entriesEl.children[idx];
        if (entry) smoothScrollTo(entry.offsetTop - 20);
      }
    });

    if (scrollHint) setTimeout(() => scrollHint.classList.add('fade'), 3000);

    observeBottomTrigger(() => {
      const layer = document.querySelector('.transition-layer');
      layer.classList.add('show');
      setTimeout(() => {
        window.location.href = 'proposal.html';
      }, 700);
    });
  } catch (err) {
    console.error(err);
    entriesEl.innerHTML = '<p>Unable to load our story right now. Please refresh.</p>';
  }
});

