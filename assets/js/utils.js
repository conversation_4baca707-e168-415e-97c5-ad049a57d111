// Utility: floating hearts background and intersection reveal
export function mountFloatingHearts(container = document.body, count = 18) {
  const wrapper = document.createElement('div');
  wrapper.className = 'hearts-bg';
  container.appendChild(wrapper);
  for (let i = 0; i < count; i++) {
    const h = document.createElement('div');
    h.className = 'heart';
    const left = Math.random() * 100;
    const size = 8 + Math.random() * 16;
    const delay = Math.random() * 6;
    const duration = 8 + Math.random() * 10;
    h.style.left = left + 'vw';
    h.style.bottom = (-10 - Math.random() * 40) + 'vh';
    h.style.width = size + 'px';
    h.style.height = size + 'px';
    h.style.animation = `floatUp ${duration}s linear ${delay}s infinite`;
    wrapper.appendChild(h);
  }
  return () => wrapper.remove();
}

export function revealOnView(selector, { root = null, rootMargin = '0px 0px -10% 0px', threshold = 0.1 } = {}) {
  const items = Array.from(document.querySelectorAll(selector));
  if (!('IntersectionObserver' in window)) {
    items.forEach(el => el.classList.add('revealed'));
    return () => {};
  }
  const io = new IntersectionObserver(entries => {
    entries.forEach(e => {
      if (e.isIntersecting) {
        e.target.classList.add('revealed');
        io.unobserve(e.target);
      }
    });
  }, { root, rootMargin, threshold });
  items.forEach(el => io.observe(el));
  return () => io.disconnect();
}

export function smoothScrollTo(el) {
  const top = typeof el === 'number' ? el : (el?.getBoundingClientRect().top + window.scrollY);
  window.scrollTo({ top, behavior: 'smooth' });
}

