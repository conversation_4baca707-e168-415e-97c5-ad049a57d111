/* Timeline page styles */
.timeline-page { min-height: 100dvh; }
.header { position: sticky; top: 0; background: linear-gradient(180deg, rgba(255,245,247,.92), rgba(255,245,247,.6)); backdrop-filter: blur(8px); border-bottom: 1px solid var(--rose-100); z-index: 5; }
.header .container { display: flex; align-items: center; justify-content: space-between; padding: 14px 0; }
.header h1 { margin: 0; font-size: 22px; }

.timeline { position: relative; display: grid; grid-template-columns: 1fr minmax(0, 2.2fr); gap: 32px; width: min(1100px, 94vw); margin: 24px auto; }

/* Axis */
.axis { position: relative; padding: 20px 0; }
.axis::before { content: ''; position: absolute; left: calc(100% - 2px); top: 0; bottom: 0; width: 2px; background: linear-gradient(var(--rose-200), var(--rose-400)); border-radius: 2px; }
.axis .tick { position: relative; padding-right: 24px; margin: 40px 0; color: var(--ink-700); font-weight: 600; }
.axis .tick::after { content: ''; position: absolute; right: -6px; top: 10px; width: 10px; height: 10px; background: var(--white); border: 2px solid var(--primary); border-radius: 50%; box-shadow: 0 0 0 4px #fff5; }

/* Entries */
.entries { position: relative; padding: 20px 0; }
.entry { display: grid; grid-template-columns: 1fr; gap: 12px; margin: 28px 0 48px; }
.card { background: #fff; border: 1px solid var(--rose-100); border-radius: 16px; overflow: hidden; box-shadow: var(--shadow-soft); transform: translateY(10px); opacity: 0; transition: transform .6s ease, opacity .6s ease; }
.card.revealed { transform: translateY(0); opacity: 1; }
.card img { width: 100%; height: 280px; object-fit: cover; display: block; }
.card .content { padding: 16px 18px 18px; }
.card .date { font-weight: 700; color: var(--primary); margin-bottom: 6px; letter-spacing: .3px; }
.card .title { font-family: 'Playfair Display', Georgia, serif; font-size: 22px; margin: 0 0 6px; }
.card .desc { color: var(--ink-700); margin: 0; }

/* Scroll hint */
.scroll-hint { text-align: center; color: var(--ink-700); margin: 10px 0 20px; font-size: 14px; opacity: 1; transition: opacity .6s ease; }
.scroll-hint.fade { opacity: 0.2; }

/* Transition layer for auto-navigation */
.transition-layer { position: fixed; inset: 0; background: var(--rose-50); z-index: 20; opacity: 0; pointer-events: none; transition: opacity .6s ease; }
.transition-layer.show { opacity: 1; }

/* Responsive */
@media (max-width: 820px) {
  .timeline { grid-template-columns: 1fr; }
  .axis::before { display: none; }
  .axis .tick { padding-right: 0; }
}
