/* Proposal page styles */
.proposal { min-height: 100dvh; display: grid; place-items: center; text-align: center; padding: 40px 0; position: relative; }
.proposal h1 { font-size: clamp(40px, 7vw, 84px); margin: 0 0 18px; }
.proposal p { color: var(--ink-700); margin: 0 0 28px; }

.yes-btn { font-size: 22px; padding: 16px 28px; background: linear-gradient(135deg, var(--primary), #ff8fb0); border-radius: 999px; box-shadow: 0 14px 36px rgba(214,51,108,.35); letter-spacing: .4px; }
.yes-btn:hover { transform: translateY(-2px) scale(1.02); }

.transition-layer { position: fixed; inset: 0; background: var(--rose-50); z-index: 10; opacity: 0; pointer-events: none; transition: opacity .6s ease; }
.transition-layer.show { opacity: 1; }

